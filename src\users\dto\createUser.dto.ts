import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>otEmpt<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, IsDateString, Matches } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export enum Gender {
    MALE = 'male',
    FEMALE = 'female',
    OTHER = 'other'
}

export class CreateUserDto {
    @ApiProperty({
        description: 'User first name',
        example: '<PERSON><PERSON>'
    })
    @IsString()
    @IsNotEmpty()
    firstName: string;

    @ApiProperty({
        description: 'User last name',
        example: 'Ali'
    })
    @IsString()
    @IsNotEmpty()
    lastName: string;

    @ApiProperty({
        description: 'User email address',
        example: '<EMAIL>'
    })
    @IsEmail()
    @IsString()
    @IsNotEmpty()
    email: string;

    @ApiProperty({
        description: 'User password (minimum 6 characters)',
        example: 'Password123!'
    })
    @IsString()
    @IsNotEmpty()
    @MinLength(6)
    password: string;

    @ApiProperty({
        description: 'User mobile number',
        example: '+1234567890'
    })
    @IsString()
    @IsNotEmpty()
    @Matches(/^\+?[1-9]\d{1,14}$/, { message: 'Mobile number must be a valid phone number' })
    mobileNumber: string;

    @ApiProperty({
        description: 'User gender',
        enum: Gender,
        example: 'male'
    })
    @IsEnum(Gender, { message: 'gender must be one of the following values: male, female, other' })
    @IsString()
    @IsNotEmpty()
    gender: Gender;

    @ApiProperty({
        description: 'User date of birth in ISO format',
        example: '1990-01-01'
    })
    @IsDateString()
    @IsNotEmpty()
    dob: Date;
}