import { <PERSON><PERSON><PERSON>, <PERSON>N<PERSON>Empt<PERSON>, <PERSON><PERSON><PERSON>, Min } from "class-validator";
import { ApiProperty } from '@nestjs/swagger';

export class signInDto {
    @ApiProperty({
        description: 'User email address',
        example: '<EMAIL>',
        format: 'email',
    })
    @IsString()
    @IsEmail()
    @IsNotEmpty()
    email: string;

    @ApiProperty({
        description: 'User password',
        example: 'SecurePassword123!',
        minLength: 6,
    })
    @IsString()
    @IsNotEmpty()
    @Min(6)
    password: string;
}