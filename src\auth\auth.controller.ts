import { Body, Controller, Post } from "@nestjs/common";
import { AuthService } from "./auth.service";
import { signUpDto } from "./dto/signUp.dto";
import { IAuth } from "./interfaces/auth.interfaces";
import {
    ApiTags,
    ApiOperation,
    ApiResponse,
    ApiBody,
    ApiBadRequestResponse,
    ApiConflictResponse
} from '@nestjs/swagger';
import { AuthResponseDto } from './dto/auth-response.dto';
import { CreateUserDto } from "src/users/dto/createUser.dto";

@ApiTags('auth')
@Controller('auth')
export class AuthController {
    constructor(private readonly authService: AuthService) {}

    @Post('signup')
    @ApiOperation({
        summary: 'User registration',
        description: 'Register a new user and return user data with access token'
    })
    @ApiBody({
        type: signUpDto,
        description: 'User registration data'
    })
    @ApiResponse({
        status: 201,
        description: 'User successfully registered',
        type: AuthResponseDto
    })
    @ApiBadRequestResponse({
        description: 'Invalid input data',
        schema: {
            type: 'object',
            properties: {
                message: { type: 'string', example: 'Validation failed' },
                error: { type: 'string', example: 'Bad Request' },
                statusCode: { type: 'number', example: 400 }
            }
        }
    })
    @ApiConflictResponse({
        description: 'User already exists',
        schema: {
            type: 'object',
            properties: {
                message: { type: 'string', example: 'User already exists' },
                error: { type: 'string', example: 'Conflict' },
                statusCode: { type: 'number', example: 409 }
            }
        }
    })
    async signUp(@Body() signUpDto: CreateUserDto): Promise<IAuth> {
        return this.authService.signUp(signUpDto);
    }
}