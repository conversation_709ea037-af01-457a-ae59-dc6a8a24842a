import { CreateUserDto, Gender } from "../../users/dto/createUser.dto";
import { ApiProperty } from '@nestjs/swagger';

export class signUpDto extends CreateUserDto {
    @ApiProperty({
        description: 'User first name',
        example: '<PERSON>',
        minLength: 1,
    })
    firstName: string;

    @ApiProperty({
        description: 'User last name',
        example: 'Do<PERSON>',
        minLength: 1,
    })
    lastName: string;

    @ApiProperty({
        description: 'User email address',
        example: '<EMAIL>',
        format: 'email',
    })
    email: string;

    @ApiProperty({
        description: 'User password',
        example: 'SecurePassword123!',
        minLength: 6,
    })
    password: string;

    @ApiProperty({
        description: 'User mobile number',
        example: '+1234567890',
    })
    mobileNumber: string;

    @ApiProperty({
        description: 'User gender',
        example: 'male',
        enum: ['male', 'female', 'other'],
    })
    gender: Gender;

    @ApiProperty({
        description: 'User date of birth',
        example: '1990-01-01',
        type: 'string',
        format: 'date',
    })
    dob: Date;
}