import { Body, Controller, Post } from "@nestjs/common";
import { UsersService } from "./users.service";
import { CreateUserDto } from "./dto/createUser.dto";
import { IUsers } from "./interfaces/users.interface";
import {
    ApiTags,
    ApiOperation,
    ApiResponse,
    ApiBody,
    ApiBadRequestResponse,
    ApiConflictResponse
} from '@nestjs/swagger';
import { UserResponseDto } from './dto/user-response.dto';

@ApiTags('users')
@Controller('users')
export class UsersController {
    constructor(private readonly usersService: UsersService) {}

    @Post()
    @ApiOperation({
        summary: 'Create a new user',
        description: 'Create a new user account with the provided information'
    })
    @ApiBody({
        type: CreateUserDto,
        description: 'User creation data'
    })
    @ApiResponse({
        status: 201,
        description: 'User successfully created',
        type: UserResponseDto
    })
    @ApiBadRequestResponse({
        description: 'Invalid input data',
        schema: {
            type: 'object',
            properties: {
                message: { type: 'string', example: 'Validation failed' },
                error: { type: 'string', example: 'Bad Request' },
                statusCode: { type: 'number', example: 400 }
            }
        }
    })
    @ApiConflictResponse({
        description: 'User already exists',
        schema: {
            type: 'object',
            properties: {
                message: { type: 'string', example: 'User already exists' },
                error: { type: 'string', example: 'Conflict' },
                statusCode: { type: 'number', example: 409 }
            }
        }
    })
    async createUser(@Body() createUserDto: CreateUserDto): Promise<Omit<IUsers, "password">> {
        return this.usersService.createUser(createUserDto);
    }
}