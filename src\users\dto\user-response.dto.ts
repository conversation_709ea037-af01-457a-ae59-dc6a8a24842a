import { ApiProperty } from '@nestjs/swagger';

export class UserResponseDto {
    @ApiProperty({
        description: 'User unique identifier',
        example: '507f1f77bcf86cd799439011',
    })
    _id: string;

    @ApiProperty({
        description: 'User first name',
        example: '<PERSON>',
    })
    firstName: string;

    @ApiProperty({
        description: 'User last name',
        example: '<PERSON><PERSON>',
    })
    lastName: string;

    @ApiProperty({
        description: 'User email address',
        example: '<EMAIL>',
    })
    email: string;

    @ApiProperty({
        description: 'User mobile number',
        example: '+1234567890',
    })
    mobileNumber: string;

    @ApiProperty({
        description: 'User gender',
        example: 'male',
        enum: ['male', 'female', 'other'],
    })
    gender: string;

    @ApiProperty({
        description: 'User date of birth',
        example: '1990-01-01T00:00:00.000Z',
    })
    dob: Date;

    @ApiProperty({
        description: 'Whether the user is verified',
        example: false,
        default: false,
    })
    isVerified: boolean;

    @ApiProperty({
        description: 'User creation timestamp',
        example: '2023-01-01T00:00:00.000Z',
    })
    createdAt: Date;

    @ApiProperty({
        description: 'User last update timestamp',
        example: '2025-01-01T00:00:00.000Z',
    })
    updatedAt: Date;
}
